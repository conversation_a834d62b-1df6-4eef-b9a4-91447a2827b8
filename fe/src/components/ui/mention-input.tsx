import React, { useState, useRef, useCallback, useEffect, useMemo, useImperativeHandle, forwardRef } from 'react'
import { User, Crown, Eye, Camera } from 'lucide-react'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { cn } from '@/lib/utils'
import { useHubParticipants } from '@/hooks/collaboration-hubs'
import { useTranslations } from '@/lib/i18n/typed-translations'
import { useCurrentUser } from '@/contexts/auth-context'
import type { HubParticipantResponse } from '@/components/collaboration-hub/types'

// Constants for better maintainability
const MENTION_INSERTION_DELAY = 50 // ms to wait before allowing new mention detection after insertion
const PARTICIPANTS_FETCH_SIZE = 100 // Number of participants to fetch
const MENTION_CHAR_REGEX = /^[a-zA-Z0-9._\s-]*$/ // Valid characters in mention text


// Enhanced participant type with disambiguation info for duplicate names
type EnhancedParticipant = HubParticipantResponse & {
  hasDuplicateName?: boolean
  disambiguationInfo?: string
}

interface MentionInputProps {
  hubId: number
  value: string // Email-based value from parent (source of truth)
  onChange: (value: string) => void // Always sends email-based value to parent
  onKeyDown?: (e: React.KeyboardEvent) => void
  placeholder?: string
  disabled?: boolean
  className?: string
  children: (props: {
    ref: React.RefObject<HTMLDivElement>
    onChange: (e: React.FormEvent<HTMLDivElement>) => void
    onKeyDown: (e: React.KeyboardEvent) => void
    onSelect: (e: React.SyntheticEvent) => void
    placeholder?: string
    disabled?: boolean
    className?: string
    contentEditable: boolean
    suppressContentEditableWarning: boolean
  }) => React.ReactNode
}

// Exposed methods for imperative API
export interface MentionInputRef {
  insertEmoji: (emoji: string) => void
}

interface MentionMatch {
  start: number
  end: number
  query: string
}

const MentionInputComponent = forwardRef<MentionInputRef, MentionInputProps>(
  (props, ref) => {
    const {
      hubId,
      value,
      onChange,
      onKeyDown,
      placeholder,
      disabled,
      className,
      children
    } = props

    const { t, keys } = useTranslations()
  const currentUser = useCurrentUser()
  const [isOpen, setIsOpen] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [mentionMatch, setMentionMatch] = useState<MentionMatch | null>(null)
  const [justInsertedMention, setJustInsertedMention] = useState(false) // Track recent mention insertion
  const [isInitialized, setIsInitialized] = useState(false) // Track if content is initialized
  const contentEditableRef = useRef<HTMLDivElement>(null)
  const popoverRef = useRef<HTMLDivElement>(null)

  const { data: participantsData, isLoading } = useHubParticipants(hubId, {
    enabled: !!hubId && !disabled,
    size: PARTICIPANTS_FETCH_SIZE
  })

  // ARCHITECTURE: Dual-State Mention System
  // - Raw State: Email-based mentions (@<EMAIL>) as single source of truth
  // - Display State: Friendly names (@UserName) in contenteditable with styled spans
  // - Participant lookup map for bidirectional conversion

  // Create participant lookup map for email ↔ name conversion
  const participantLookup = useMemo(() => {
    const map = new Map<string, HubParticipantResponse>()
    participantsData?.content?.forEach(p => {
      if (p.email) {
        map.set(p.email.toLowerCase(), p)
      }
    })
    return map
  }, [participantsData?.content])

  const filteredParticipants = useMemo((): EnhancedParticipant[] => {
    if (!mentionMatch) return []

    const participants = participantsData?.content || []

    // Filter out current user and apply search query
    const filtered = participants.filter(p => {
      // Exclude current user from suggestions
      // Handle both internal users (with user.id) and external participants (email-only)

      // Primary check: Compare by email (works for both internal and external users)
      if (currentUser?.email && p.email) {
        const currentUserEmail = currentUser.email.toLowerCase().trim()
        const participantEmail = p.email.toLowerCase().trim()
        if (currentUserEmail === participantEmail) {
          return false
        }
      }

      // Secondary check: For internal users, also compare by ID as fallback
      // This handles edge cases where email might not match but ID does
      if (currentUser?.id && p.id && typeof currentUser.id === 'number' && typeof p.id === 'number') {
        if (currentUser.id === p.id) {
          return false
        }
      }

      const query = mentionMatch.query.toLowerCase()
      const emailPrefix = p.email?.split('@')[0]?.toLowerCase() || ''
      const name = p.name?.toLowerCase() || ''

      // Prioritize exact email prefix matches
      return emailPrefix.includes(query) || name.includes(query)
    }).sort((a, b) => {
      const query = mentionMatch.query.toLowerCase()
      const aEmailPrefix = a.email?.split('@')[0]?.toLowerCase() || ''
      const bEmailPrefix = b.email?.split('@')[0]?.toLowerCase() || ''

      // Prioritize exact email prefix matches first
      const aEmailMatch = aEmailPrefix.startsWith(query)
      const bEmailMatch = bEmailPrefix.startsWith(query)

      if (aEmailMatch && !bEmailMatch) return -1
      if (!aEmailMatch && bEmailMatch) return 1

      // Then prioritize email prefix contains over name matches
      const aEmailContains = aEmailPrefix.includes(query)
      const bEmailContains = bEmailPrefix.includes(query)

      if (aEmailContains && !bEmailContains) return -1
      if (!aEmailContains && bEmailContains) return 1

      return 0
    })

    // ARCHITECTURE: Duplicate Name Handling
    // Detect participants with duplicate display names and add disambiguation info
    const nameCount = new Map<string, number>()
    filtered.forEach(p => {
      const name = p.name || 'Unknown'
      nameCount.set(name, (nameCount.get(name) || 0) + 1)
    })

    // Add disambiguation info for participants with duplicate names
    return filtered.map(p => ({
      ...p,
      hasDuplicateName: (nameCount.get(p.name || 'Unknown') || 0) > 1,
      disambiguationInfo: (nameCount.get(p.name || 'Unknown') || 0) > 1
        ? p.email?.split('@')[0] || p.email || 'Unknown'
        : undefined
    }))
  }, [mentionMatch, participantsData?.content, currentUser?.email, currentUser?.id])



  // Convert contenteditable DOM to email-based format for backend
  const convertDisplayMentionsToEmail = useCallback((element: HTMLElement): string => {
    // Recursive function to traverse the entire DOM tree
    const traverseNode = (node: Node): string => {
      if (node.nodeType === Node.TEXT_NODE) {
        return node.textContent || ''
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        const el = node as HTMLElement

        // Check if this is a mention token
        if (el.classList.contains('mention-token')) {
          const email = el.dataset.email
          if (email && email.trim()) {
            // Successfully found mention with email data
            return `@${email}`
          } else {
            // Mention token without proper email data - this is the bug!
            // Log for debugging and try to extract email from text content
            console.warn('Mention token found without proper email data:', {
              element: el,
              textContent: el.textContent,
              dataset: el.dataset,
              className: el.className
            })

            // Fallback: try to extract email from text content if it looks like an email
            const textContent = el.textContent || ''
            const emailMatch = textContent.match(/@([a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/)
            if (emailMatch) {
              return `@${emailMatch[1]}`
            }

            // If no email found, this mention token is broken - return as display text
            // This should not happen in a properly functioning system
            console.error('Broken mention token - no email data available:', el)
            return textContent
          }
        } else {
          // Regular element - recursively process children
          let result = ''
          for (const child of el.childNodes) {
            result += traverseNode(child)
          }
          return result
        }
      }

      return ''
    }

    const result = traverseNode(element)

    // Debug logging to help identify conversion issues (only log if there are issues)
    try {
      const mentionTokenCount = element.querySelectorAll?.('.mention-token')?.length || 0
      if (mentionTokenCount > 0) {
        console.log('convertDisplayMentionsToEmail:', {
          input: element.innerHTML,
          output: result,
          mentionTokens: mentionTokenCount
        })
      }
    } catch (error) {
      // Ignore querySelectorAll errors in test environments
      console.warn('querySelectorAll error in convertDisplayMentionsToEmail:', error)
    }

    return result
  }, [])

  // Convert email-based content to HTML with mention spans
  const convertEmailContentToHTML = useCallback((emailContent: string): string => {
    if (!emailContent) return ''

    // First, escape HTML characters for safety
    let html = emailContent
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;')

    // BULLETPROOF FIX: Process the string character by character to avoid regex issues
    // This ensures we only match complete email mentions at word boundaries
    let result = ''
    let i = 0

    while (i < html.length) {
      if (html[i] === '@') {
        // Check if this @ is at the start or after whitespace (valid mention start)
        const isValidStart = i === 0 || /\s/.test(html[i - 1])

        if (isValidStart) {
          // Try to match a complete email from this position
          const remainingText = html.slice(i + 1)
          const emailMatch = remainingText.match(/^([a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/)

          if (emailMatch) {
            const email = emailMatch[1]
            // Check if this email ends at a word boundary (space or end of string)
            const emailEndIndex = i + 1 + email.length
            const isValidEnd = emailEndIndex >= html.length || /\s/.test(html[emailEndIndex])

            if (isValidEnd) {
              // This is a valid complete email mention
              const participant = participantLookup.get(email.toLowerCase())
              const displayName = participant?.name || email.split('@')[0] || email

              // Use HTML entities for the span attributes to avoid XSS
              const escapedEmail = email.replace(/"/g, '&quot;')
              const escapedDisplayName = displayName.replace(/</g, '&lt;').replace(/>/g, '&gt;')

              // Debug logging for HTML conversion (only when needed)
              if (!participant) {
                console.log('Converting email to HTML mention (no participant found):', {
                  email: email,
                  displayName: displayName,
                  position: i
                })
              }

              // Add the mention span to result
              result += `<span class="mention-token" contenteditable="false" data-email="${escapedEmail}">@${escapedDisplayName}</span>`

              // Skip past the entire email
              i = emailEndIndex
              continue
            }
          }
        }
      }

      // Not a valid email mention, add the character as-is
      result += html[i]
      i++
    }

    html = result

    return html
  }, [participantLookup])

  // Cursor position management utilities
  const saveCursorPosition = useCallback(() => {
    const selection = window.getSelection()
    if (!selection || selection.rangeCount === 0) return null

    const range = selection.getRangeAt(0)
    return {
      startContainer: range.startContainer,
      startOffset: range.startOffset,
      endContainer: range.endContainer,
      endOffset: range.endOffset
    }
  }, [])

  const restoreCursorPosition = useCallback((savedPosition: {
    startContainer: Node;
    startOffset: number;
    endContainer: Node;
    endOffset: number;
  } | null) => {
    if (!savedPosition) return

    try {
      const selection = window.getSelection()
      if (!selection) return

      const range = document.createRange()
      range.setStart(savedPosition.startContainer, savedPosition.startOffset)
      range.setEnd(savedPosition.endContainer, savedPosition.endOffset)

      selection.removeAllRanges()
      selection.addRange(range)
    } catch (_error) {
      // Ignore errors if nodes are no longer valid
    }
  }, [])

  // Insert emoji at current cursor position in contenteditable
  const insertEmojiAtCursor = useCallback((emoji: string) => {
    if (!contentEditableRef.current || disabled) return

    const element = contentEditableRef.current
    const selection = window.getSelection()

    if (!selection || selection.rangeCount === 0) {
      // No selection, append to end
      const textNode = document.createTextNode(emoji)
      element.appendChild(textNode)

      // Position cursor after the emoji
      const range = document.createRange()
      range.setStartAfter(textNode)
      range.collapse(true)
      const sel = window.getSelection()
      if (sel) {
        sel.removeAllRanges()
        sel.addRange(range)
      }
    } else {
      const range = selection.getRangeAt(0)

      // Use the existing insertMentionToken logic but adapted for emoji
      // This ensures we use the same robust cursor handling as mentions

      // Find the exact insertion point using the same logic as mention detection
      const textContent = element.textContent || ''

      // Inline cursor position calculation to avoid dependency issues
      let cursor = 0
      const walker = document.createTreeWalker(
        element,
        NodeFilter.SHOW_TEXT,
        null
      )

      let node
      while ((node = walker.nextNode())) {
        if (node === range.startContainer) {
          cursor += range.startOffset
          break
        }
        cursor += node.textContent?.length || 0
      }

      console.log('Inserting emoji at cursor position:', { cursor, textContent: textContent.slice(Math.max(0, cursor - 10), cursor + 10) })

      // Find the text node that contains our cursor position
      let targetTextNode: Node | null = null
      let nodeOffset = 0
      let currentPos = 0

      const walker2 = document.createTreeWalker(
        element,
        NodeFilter.SHOW_TEXT,
        null
      )

      let node2
      while ((node2 = walker2.nextNode())) {
        const nodeLength = node2.textContent?.length || 0
        if (currentPos <= cursor && currentPos + nodeLength >= cursor) {
          targetTextNode = node2
          nodeOffset = cursor - currentPos
          break
        }
        currentPos += nodeLength
      }

      if (targetTextNode && targetTextNode.nodeType === Node.TEXT_NODE) {
        // Split the text node and insert the emoji
        const textNode = targetTextNode as Text
        const beforeText = textNode.textContent?.slice(0, nodeOffset) || ''
        const afterText = textNode.textContent?.slice(nodeOffset) || ''

        const parentNode = textNode.parentNode
        if (parentNode) {
          // Create new nodes
          const beforeTextNode = beforeText ? document.createTextNode(beforeText) : null
          const emojiNode = document.createTextNode(emoji)
          const afterTextNode = afterText ? document.createTextNode(afterText) : null

          // Replace the original text node
          if (beforeTextNode) {
            parentNode.insertBefore(beforeTextNode, textNode)
          }
          parentNode.insertBefore(emojiNode, textNode)
          if (afterTextNode) {
            parentNode.insertBefore(afterTextNode, textNode)
          }
          parentNode.removeChild(textNode)

          // Position cursor after the emoji
          const newRange = document.createRange()
          newRange.setStartAfter(emojiNode)
          newRange.collapse(true)
          selection.removeAllRanges()
          selection.addRange(newRange)
        }
      } else {
        // Fallback: insert at the end of the element
        const emojiNode = document.createTextNode(emoji)
        element.appendChild(emojiNode)

        const newRange = document.createRange()
        newRange.setStartAfter(emojiNode)
        newRange.collapse(true)
        selection.removeAllRanges()
        selection.addRange(newRange)
      }
    }

    // Convert updated DOM to email format and notify parent
    const newEmailContent = convertDisplayMentionsToEmail(element)
    onChange(newEmailContent)

    // Focus the element to ensure cursor is visible
    element.focus()

    console.log('Emoji inserted:', { emoji, newContent: newEmailContent })
  }, [disabled, convertDisplayMentionsToEmail, onChange, saveCursorPosition])

  // Expose imperative API for emoji insertion
  useImperativeHandle(ref, () => ({
    insertEmoji: insertEmojiAtCursor
  }), [insertEmojiAtCursor])

  // Initialize content when value changes (only for initial load or external updates)
  useEffect(() => {
    if (!contentEditableRef.current) return

    const element = contentEditableRef.current
    const currentContent = convertDisplayMentionsToEmail(element)

    // Only update if the content is different and we haven't initialized yet
    // or if this is an external update (value changed but not from user input)
    if (currentContent !== value && (!isInitialized || element.textContent === '')) {
      const savedPosition = saveCursorPosition()
      element.innerHTML = convertEmailContentToHTML(value)
      restoreCursorPosition(savedPosition)
      setIsInitialized(true)
    }
  }, [value, convertEmailContentToHTML, convertDisplayMentionsToEmail, saveCursorPosition, restoreCursorPosition, isInitialized])

  // Manage popper visibility based on mention match and available participants
  useEffect(() => {
    // Only show popper if there's a mention match AND there are participants to show
    // AND we haven't just inserted a mention (to prevent immediate reopening)
    const shouldShowPopper = !!mentionMatch && filteredParticipants.length > 0 && !justInsertedMention
    setIsOpen(shouldShowPopper)

    // Debug logging for dropdown state
    if (mentionMatch && justInsertedMention) {
      console.log('Dropdown blocked due to recent mention insertion')
    }
  }, [mentionMatch, filteredParticipants.length, justInsertedMention])

  // Get cursor position in text content (ignoring HTML tags)
  const getCursorPositionInText = useCallback((element: HTMLElement, range: Range): number => {
    let position = 0
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      null
    )

    let node
    while ((node = walker.nextNode())) {
      if (node === range.startContainer) {
        return position + range.startOffset
      }
      position += node.textContent?.length || 0
    }

    return position
  }, [])

  // Find mention match in contenteditable content
  const findMentionMatchInContentEditable = useCallback((element: HTMLElement, cursor: number): MentionMatch | null => {
    // BUGFIX: Check if cursor is positioned right after a completed mention token
    // This prevents the dropdown from incorrectly appearing when user deletes space after a mention
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      const { startContainer, startOffset } = range

      // Helper function to check if a node is a mention token
      const isMentionToken = (node: Node | null): boolean => {
        return node?.nodeType === Node.ELEMENT_NODE &&
               (node as Element).classList.contains('mention-token')
      }

      // Case 1: Cursor is in a text node - check if previous sibling is a mention token
      if (startContainer.nodeType === Node.TEXT_NODE) {
        // If cursor is at the beginning of a text node, check previous sibling
        if (startOffset === 0 && isMentionToken(startContainer.previousSibling)) {
          console.log('Cursor detected right after mention token (text node case)')
          return null
        }
      }

      // Case 2: Cursor is directly in the contenteditable element
      else if (startContainer === element) {
        // Check if the node right before cursor position is a mention token
        if (startOffset > 0) {
          const nodeBeforeCursor = startContainer.childNodes[startOffset - 1]
          if (isMentionToken(nodeBeforeCursor)) {
            console.log('Cursor detected right after mention token (element case)')
            return null
          }
        }
      }
    }

    const textContent = element.textContent || ''
    const before = textContent.slice(0, cursor)

    // Find the last @ symbol before cursor
    const lastAtIndex = before.lastIndexOf('@')
    if (lastAtIndex === -1) return null

    // Get text after the @ symbol up to cursor
    const afterAt = before.slice(lastAtIndex + 1)

    // Check if this looks like a complete email address
    const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/
    const textAfterAt = textContent.slice(lastAtIndex + 1)
    if (emailPattern.test(textAfterAt)) {
      // This is a complete email address, not a new mention
      return null
    }

    // Only exclude mentions that end with a space (user pressed space after completing a mention)
    if (afterAt.endsWith(' ')) {
      return null
    }

    // Match valid mention characters (letters, numbers, dots, underscores, spaces for multi-word names)
    if (MENTION_CHAR_REGEX.test(afterAt)) {
      return {
        start: lastAtIndex,
        end: cursor,
        query: afterAt
      }
    }

    return null
  }, [])

  // Handle click outside to close popup
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isOpen &&
          popoverRef.current &&
          !popoverRef.current.contains(event.target as Node) &&
          contentEditableRef.current &&
          !contentEditableRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setMentionMatch(null)
        setSelectedIndex(0)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  const getInitials = (name?: string) => {
    if (!name) return '?'
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Crown className="h-3 w-3 text-yellow-600" />
      case 'reviewer':
        return <Eye className="h-3 w-3 text-blue-600" />
      case 'reviewer_creator':
        return <Camera className="h-3 w-3 text-purple-600" />
      case 'content_creator':
        return <User className="h-3 w-3 text-green-600" />
      default:
        return <User className="h-3 w-3 text-gray-600" />
    }
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin':
        return t(keys.collaborationHubs.roles.admin)
      case 'reviewer':
        return t(keys.collaborationHubs.roles.reviewer)
      case 'reviewer_creator':
        return t(keys.collaborationHubs.roles.reviewer_creator)
      case 'content_creator':
        return t(keys.collaborationHubs.roles.content_creator)
      default:
        return role
    }
  }

  // Handle contenteditable input events
  const handleContentEditableInput = useCallback((e: React.FormEvent<HTMLDivElement>) => {
    const element = e.currentTarget

    // Convert DOM to email format and notify parent
    const emailContent = convertDisplayMentionsToEmail(element)
    onChange(emailContent)

    // Update mention detection based on cursor position
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      const cursor = getCursorPositionInText(element, range)
      const match = findMentionMatchInContentEditable(element, cursor)

      // If user typed a space and dropdown is open, close it (completed mention)
      if (match === null && isOpen) {
        setIsOpen(false)
        setMentionMatch(null)
        setSelectedIndex(0)
        return
      }

      // If we just inserted a mention, don't immediately reopen dropdown
      if (justInsertedMention) {
        console.log('Mention just inserted, skipping mention detection')
        return
      }

      // Only update mention match if it's different from the current one
      if (!mentionMatch ||
          !match ||
          match.start !== mentionMatch.start ||
          match.end !== mentionMatch.end ||
          match.query !== mentionMatch.query) {
        setMentionMatch(match)
        setSelectedIndex(0)
      }
    }
  }, [onChange, convertDisplayMentionsToEmail, getCursorPositionInText, findMentionMatchInContentEditable, isOpen, justInsertedMention, mentionMatch])





  // Insert mention token as styled span
  const insertMentionToken = useCallback((participant: HubParticipantResponse) => {
    if (!mentionMatch || !contentEditableRef.current) return

    const selection = window.getSelection()
    if (!selection || selection.rangeCount === 0) return

    const element = contentEditableRef.current

    // Validate participant has email - this is critical for the dual-state system
    if (!participant.email || !participant.email.trim()) {
      console.error('Cannot insert mention token - participant missing email:', participant)
      return
    }

    // SIMPLE AND RELIABLE FIX: Use direct DOM replacement to avoid position mapping issues

    // 1. Find the text node containing the @ symbol and replace the mention query directly

    // 2. Find the text node that contains our @ symbol
    let targetTextNode = null
    let nodeOffset = 0

    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      null
    )

    let currentPos = 0
    let node
    while ((node = walker.nextNode())) {
      const nodeLength = node.textContent?.length || 0
      if (currentPos <= mentionMatch.start && currentPos + nodeLength > mentionMatch.start) {
        targetTextNode = node
        nodeOffset = mentionMatch.start - currentPos
        break
      }
      currentPos += nodeLength
    }

    if (targetTextNode && targetTextNode.nodeType === Node.TEXT_NODE) {
      // 3. Create the mention span
      const mentionSpan = document.createElement('span')
      mentionSpan.contentEditable = 'false'
      mentionSpan.className = 'mention-token'
      mentionSpan.dataset.email = participant.email.trim()
      mentionSpan.textContent = `@${participant.name || participant.email.split('@')[0] || 'Unknown'}`

      // 4. Split the text node and insert the mention
      const beforeText = targetTextNode.textContent?.slice(0, nodeOffset) || ''
      const afterText = targetTextNode.textContent?.slice(nodeOffset + (mentionMatch.end - mentionMatch.start)) || ''

      const parentNode = targetTextNode.parentNode
      if (parentNode) {
        // Create new nodes
        const beforeTextNode = document.createTextNode(beforeText)
        const spaceNode = document.createTextNode(' ')
        const afterTextNode = afterText ? document.createTextNode(afterText) : null

        // Replace the original text node
        parentNode.insertBefore(beforeTextNode, targetTextNode)
        parentNode.insertBefore(mentionSpan, targetTextNode)
        parentNode.insertBefore(spaceNode, targetTextNode)
        if (afterTextNode) {
          parentNode.insertBefore(afterTextNode, targetTextNode)
        }
        parentNode.removeChild(targetTextNode)

        // 5. Position cursor after the space
        const newRange = document.createRange()
        newRange.setStartAfter(spaceNode)
        newRange.collapse(true)
        selection.removeAllRanges()
        selection.addRange(newRange)
      }
    }

    // 6. Get the final email content from the updated DOM
    const newEmailContent = convertDisplayMentionsToEmail(element)

    // Debug logging for mention creation
    console.log('Creating mention token:', {
      participant: participant,
      email: participant.email,
      displayName: participant.name,
      mentionMatch: mentionMatch,
      newEmailContent: newEmailContent
    })



    // 5. Notify parent with the new email content
    onChange(newEmailContent)

    // FORCE CLOSE dropdown and mark that we just inserted a mention
    setIsOpen(false)
    setMentionMatch(null)
    setSelectedIndex(0)
    setJustInsertedMention(true)

    // Clear the flag after a short delay to allow normal mention detection to resume
    setTimeout(() => {
      setJustInsertedMention(false)
      console.log('Mention insertion cooldown ended, mention detection resumed')
    }, MENTION_INSERTION_DELAY)

    // Additional safety: force close dropdown after a brief delay to handle any race conditions
    setTimeout(() => {
      setIsOpen(false)
      setMentionMatch(null)
    }, 10)
  }, [mentionMatch, convertDisplayMentionsToEmail, onChange, convertEmailContentToHTML])



  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    // Handle backspace/delete for mention tokens when dropdown is not open
    if (!isOpen && (e.key === 'Backspace' || e.key === 'Delete')) {
      const selection = window.getSelection()
      if (selection && selection.rangeCount > 0 && contentEditableRef.current) {
        const range = selection.getRangeAt(0)
        const { startContainer, startOffset } = range

        // Handle backspace at the beginning of text nodes (cursor right after mention token)
        if (e.key === 'Backspace' && startContainer.nodeType === Node.TEXT_NODE && startOffset === 0) {
          const previousSibling = startContainer.previousSibling
          if (previousSibling && previousSibling.nodeType === Node.ELEMENT_NODE) {
            const element = previousSibling as Element
            if (element.classList.contains('mention-token')) {
              e.preventDefault()
              // Remove the mention token
              element.remove()
              // Update the parent with new content
              const newEmailContent = convertDisplayMentionsToEmail(contentEditableRef.current)
              onChange(newEmailContent)
              console.log('Mention token deleted via backspace')
              return
            }
          }
        }

        // Handle delete at the end of text nodes (cursor right before mention token)
        if (e.key === 'Delete' && startContainer.nodeType === Node.TEXT_NODE) {
          const textContent = startContainer.textContent || ''
          if (startOffset === textContent.length) {
            const nextSibling = startContainer.nextSibling
            if (nextSibling && nextSibling.nodeType === Node.ELEMENT_NODE) {
              const element = nextSibling as Element
              if (element.classList.contains('mention-token')) {
                e.preventDefault()
                // Remove the mention token
                element.remove()
                // Update the parent with new content
                const newEmailContent = convertDisplayMentionsToEmail(contentEditableRef.current)
                onChange(newEmailContent)
                console.log('Mention token deleted via delete key')
                return
              }
            }
          }
        }

        // Handle backspace when cursor is directly in contenteditable element
        if (e.key === 'Backspace' && startContainer === contentEditableRef.current && startOffset > 0) {
          const nodeBeforeCursor = startContainer.childNodes[startOffset - 1]
          if (nodeBeforeCursor && nodeBeforeCursor.nodeType === Node.ELEMENT_NODE) {
            const element = nodeBeforeCursor as Element
            if (element.classList.contains('mention-token')) {
              e.preventDefault()
              // Remove the mention token
              element.remove()
              // Update the parent with new content
              const newEmailContent = convertDisplayMentionsToEmail(contentEditableRef.current)
              onChange(newEmailContent)
              console.log('Mention token deleted via backspace (direct element case)')
              return
            }
          }
        }
      }
    }

    if (isOpen && filteredParticipants.length > 0) {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault()
          setSelectedIndex(prev => (prev + 1) % filteredParticipants.length)
          break
        case 'ArrowUp':
          e.preventDefault()
          setSelectedIndex(prev => (prev - 1 + filteredParticipants.length) % filteredParticipants.length)
          break
        case 'Enter':
        case 'Tab':
          e.preventDefault()
          insertMentionToken(filteredParticipants[selectedIndex])
          break
        case 'Escape':
          e.preventDefault()
          setIsOpen(false)
          setMentionMatch(null)
          setSelectedIndex(0)
          break
        case ' ':
          // Space key - close the popup if we're at the end of a mention
          if (mentionMatch) {
            setIsOpen(false)
            setMentionMatch(null)
            setSelectedIndex(0)
          }
          onKeyDown?.(e)
          break
        default:
          onKeyDown?.(e)
      }
    } else {
      onKeyDown?.(e)
    }
  }, [isOpen, filteredParticipants, selectedIndex, mentionMatch, onKeyDown, insertMentionToken, convertDisplayMentionsToEmail, onChange])

  const handleParticipantSelect = useCallback((e: React.MouseEvent, p: HubParticipantResponse) => {
    e.preventDefault()
    e.stopPropagation()
    insertMentionToken(p)
  }, [insertMentionToken])

  const handleSelect = useCallback(() => {
    // Don't interfere with mention selection when popup is open
    if (isOpen) {
      return
    }

    // Update mention match based on cursor position
    setTimeout(() => {
      if (!contentEditableRef.current) return

      const selection = window.getSelection()
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        const cursor = getCursorPositionInText(contentEditableRef.current, range)
        const match = findMentionMatchInContentEditable(contentEditableRef.current, cursor)

        if (match) {
          setMentionMatch(match)
          setSelectedIndex(0)
        } else if (mentionMatch) {
          setMentionMatch(null)
          setSelectedIndex(0)
        }
      }
    }, 0)
  }, [getCursorPositionInText, findMentionMatchInContentEditable, mentionMatch, isOpen])

  return (
    <div className={cn("relative", className)}>
      {children({
        ref: contentEditableRef,
        onChange: handleContentEditableInput,
        onKeyDown: handleKeyDown,
        onSelect: handleSelect,
        placeholder,
        disabled,
        className,
        contentEditable: !disabled,
        suppressContentEditableWarning: true
      })}



      {isOpen && (
        <div
          ref={popoverRef}
          className="absolute z-50 w-80 bottom-full mb-1 bg-popover border border-border rounded-md shadow-md"
        >
          <div className="p-2">
            {isLoading ? (
              <div className="text-center py-4 text-sm text-muted-foreground">
                {t(keys.ui.mentionInput.loadingParticipants)}
              </div>
            ) : (
              <ScrollArea className="max-h-64">
                {filteredParticipants.map((p, index) => (
                  <div
                    key={p.id}
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      handleParticipantSelect(e, p)
                    }}
                    className={cn(
                      "flex items-center gap-3 p-3 cursor-pointer rounded-md hover:bg-accent",
                      index === selectedIndex && "bg-accent"
                    )}
                  >
                    <Avatar className="h-8 w-8">
                      <AvatarFallback className="text-xs">
                        {getInitials(p.name)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <p className="font-medium text-sm truncate">
                          {p.name || 'Unknown'}
                          {p.hasDuplicateName && p.disambiguationInfo && (
                            <span className="text-muted-foreground font-normal ml-1">
                              ({p.disambiguationInfo})
                            </span>
                          )}
                        </p>
                        {p.role && <span>{getRoleIcon(p.role)}</span>}
                      </div>
                      <div className="flex items-center gap-2 mt-1">
                        <p className="text-xs text-muted-foreground truncate">
                          {p.email}
                        </p>
                        {p.role && (
                          <Badge variant="secondary" className="text-xs px-1 py-0">
                            {getRoleLabel(p.role)}
                          </Badge>
                        )}
                        {p.isExternal && (
                          <Badge variant="outline" className="text-xs px-1 py-0">
                            External
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </ScrollArea>
            )}
          </div>
        </div>
      )}
    </div>
  )
})

// Export memoized component with proper comparison
export const MentionInput = React.memo(MentionInputComponent, (prevProps, nextProps) => {
  // Custom comparison function to prevent unnecessary re-renders
  return (
    prevProps.hubId === nextProps.hubId &&
    prevProps.value === nextProps.value &&
    prevProps.placeholder === nextProps.placeholder &&
    prevProps.disabled === nextProps.disabled &&
    prevProps.className === nextProps.className &&
    prevProps.onChange === nextProps.onChange &&
    prevProps.onKeyDown === nextProps.onKeyDown
  )
})
